import os
from pathlib import Path

# Base paths
PROJECT_ROOT = Path(__file__).parent
DATA_DIR = PROJECT_ROOT / "data"
LOGS_DIR = PROJECT_ROOT / "logs"

# Create directories
DATA_DIR.mkdir(exist_ok=True)
LOGS_DIR.mkdir(exist_ok=True)

# Personality Configuration (NEW)
PERSONALITY_CONFIG = {
    "current_personality": "concise",  # Default personality
    "personalities": {
        "concise": {
            "name": "Mobius",
            "description": "a highly efficient AI assistant",
            "instructions": """
- Be EXTREMELY concise - aim for 1-3 sentences unless more detail is explicitly requested
- Answer directly without preambles or unnecessary explanations
- Skip pleasantries and get to the point
- Only elaborate when asked "explain more", "details", or similar
- For yes/no questions, start with yes/no then add ONE clarifying sentence if needed
- Avoid phrases like "I understand", "Great question", "Let me explain"
- Don't repeat what the user said back to them""",
            "response_style": "Direct, efficient, no fluff",
            "max_response_length": 50  # words
        },
        "friendly": {
            "name": "Mobius",
            "description": "a warm and helpful AI companion",
            "instructions": """
- Be friendly but still reasonably concise (3-5 sentences typical)
- Use the user's name when you know it
- Show enthusiasm when appropriate
- Balance friendliness with efficiency""",
            "response_style": "Warm, engaging, personable",
            "max_response_length": 100
        },
        "technical": {
            "name": "Mobius",
            "description": "a technical expert AI",
            "instructions": """
- Provide technically accurate, detailed responses
- Use proper terminology
- Include code examples when relevant
- Structure responses with clear sections
- Be thorough but organized""",
            "response_style": "Precise, detailed, technical",
            "max_response_length": 200
        },
        "creative": {
            "name": "Mobius",
            "description": "a creative AI collaborator",
            "instructions": """
- Be imaginative and think outside the box
- Offer multiple creative solutions
- Use analogies and metaphors
- Encourage brainstorming""",
            "response_style": "Creative, inspirational, imaginative",
            "max_response_length": 150
        }
    }
}

# Model Configuration
MODEL_CONFIG = {
    "model_name": "meta-llama/Meta-Llama-3.1-8B-Instruct",
    "device": "cuda",
    "load_in_4bit": True,
    "use_flash_attention": False,  # Set to False for stability
    "max_batch_size": 1,
}

# Generation Parameters (Optimized for conciseness)
GENERATION_CONFIG = {
    "max_new_tokens": 512,  # Reduced for faster responses
    "temperature": 0.6,  # Lower for more focused responses
    "top_p": 0.85,  # Tighter nucleus
    "top_k": 30,  # Fewer options
    "do_sample": True,
    "repetition_penalty": 1.15,
    "no_repeat_ngram_size": 3,
    "length_penalty": 0.8,  # Prefer shorter responses
    "early_stopping": True,
}

# Memory Configuration (Enhanced)
MEMORY_CONFIG = {
    "max_short_term": 5,  # Recent exchanges to keep
    "max_important_info": 20,  # Explicitly remembered items
    "max_user_facts": 50,  # Facts about the user
    "context_token_budget": 500,  # Reduced for efficiency
    "auto_summarize": True,
    "persistence_file": DATA_DIR / "mobius_memory.json"
}

# Web Search Configuration
WEB_SEARCH_CONFIG = {
    "default_engine": "duckduckgo",  # Privacy-focused
    "max_results": 3,
    "timeout": 10,
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "rate_limit": 1.0  # Seconds between requests
}

# TTS Configuration
TTS_CONFIG = {
    "enabled": False,  # Default off
    "engine": "pyttsx3",  # or "gTTS" for Google TTS
    "voice_rate": 180,  # Words per minute
    "volume": 0.9,
    "voice_gender": "female",  # or "male"
    "streaming_mode": True,  # Stream speech as text generates
    "sentence_buffer": True,  # Speak complete sentences
}

# Performance Settings
PERFORMANCE_CONFIG = {
    "use_cache": True,
    "clear_cache_after_generation": False,
    "max_context_length": 2048,  # Reduced for speed
    "batch_size": 1,
    "num_threads": 4,
    "response_timeout": 30,
}

# System Settings
SYSTEM_CONFIG = {
    "allow_file_operations": True,
    "allow_web_access": True,
    "safe_mode": True,  # Restrict dangerous operations
    "auto_save_memory": True,
    "save_interval": 5,  # Save memory every 5 exchanges
}

# API Keys (from environment)
API_KEYS = {
    "huggingface": os.getenv("HF_TOKEN"),
    "openai": os.getenv("OPENAI_API_KEY"),  # For fallback
    "serper": os.getenv("SERPER_API_KEY"),  # For better search
    "elevenlabs": os.getenv("ELEVENLABS_API_KEY"),  # For better TTS
}

# Quick Settings Presets
PRESETS = {
    "fast": {
        "description": "Fastest responses, may be less detailed",
        "max_new_tokens": 256,
        "temperature": 0.5,
        "personality": "concise"
    },
    "balanced": {
        "description": "Balance between speed and quality",
        "max_new_tokens": 512,
        "temperature": 0.6,
        "personality": "friendly"
    },
    "quality": {
        "description": "Best quality, may be slower",
        "max_new_tokens": 1024,
        "temperature": 0.7,
        "personality": "technical"
    }
}

def apply_preset(preset_name: str):
    """Apply a configuration preset"""
    if preset_name in PRESETS:
        preset = PRESETS[preset_name]
        GENERATION_CONFIG["max_new_tokens"] = preset["max_new_tokens"]
        GENERATION_CONFIG["temperature"] = preset["temperature"]
        PERSONALITY_CONFIG["current_personality"] = preset["personality"]
        return True
    return False

def get_current_settings():
    """Get current configuration summary

import os
from pathlib import Path

# Base paths
PROJECT_ROOT = Path(__file__).parent
DATA_DIR = PROJECT_ROOT / "data"
LOGS_DIR = PROJECT_ROOT / "logs"

# Create directories
DATA_DIR.mkdir(exist_ok=True)
LOGS_DIR.mkdir(exist_ok=True)

# Personality Configuration (NEW)
PERSONALITY_CONFIG = {
    "current_personality": "concise",  # Default personality
    "personalities": {
        "concise": {
            "name": "Mobius",
            "description": "a highly efficient AI assistant",
            "instructions": """
- Be EXTREMELY concise - aim for 1-3 sentences unless more detail is explicitly requested
- Answer directly without preambles or unnecessary explanations
- Skip pleasantries and get to the point
- Only elaborate when asked "explain more", "details", or similar
- For yes/no questions, start with yes/no then add ONE clarifying sentence if needed
- Avoid phrases like "I understand", "Great question", "Let me explain"
- Don't repeat what the user said back to them""",
            "response_style": "Direct, efficient, no fluff",
            "max_response_length": 50  # words
        },
        "friendly": {
            "name": "Mobius",
            "description": "a warm and helpful AI companion",
            "instructions": """
- Be friendly but still reasonably concise (3-5 sentences typical)
- Use the user's name when you know it
- Show enthusiasm when appropriate
- Balance friendliness with efficiency""",
            "response_style": "Warm, engaging, personable",
            "max_response_length": 100
        },
        "technical": {
            "name": "Mobius",
            "description": "a technical expert AI",
            "instructions": """
- Provide technically accurate, detailed responses
- Use proper terminology
- Include code examples when relevant
- Structure responses with clear sections
- Be thorough but organized""",
            "response_style": "Precise, detailed, technical",
            "max_response_length": 200
        },
        "creative": {
            "name": "Mobius",
            "description": "a creative AI collaborator",
            "instructions": """
- Be imaginative and think outside the box
- Offer multiple creative solutions
- Use analogies and metaphors
- Encourage brainstorming""",
            "response_style": "Creative, inspirational, imaginative",
            "max_response_length": 150
        }
    }
}

# Model Configuration
MODEL_CONFIG = {
    "model_name": "meta-llama/Meta-Llama-3.1-8B-Instruct",
    "device": "cuda",
    "load_in_4bit": True,
    "use_flash_attention": False,  # Set to False for stability
    "max_batch_size": 1,
}

# LLM Engine Configuration (for core module compatibility)
LLM_CONFIG = {
    "model_name": "meta-llama/Meta-Llama-3.1-8B-Instruct",
    "device": "cuda",
    "max_length": 2048,
    "temperature": 0.6,
    "top_p": 0.85,
    "do_sample": True,
    "quantization": {
        "enabled": True,
        "type": "nf4"
    }
}

# Generation Parameters (Optimized for conciseness)
GENERATION_CONFIG = {
    "max_new_tokens": 512,  # Reduced for faster responses
    "temperature": 0.6,  # Lower for more focused responses
    "top_p": 0.85,  # Tighter nucleus
    "top_k": 30,  # Fewer options
    "do_sample": True,
    "repetition_penalty": 1.15,
    "no_repeat_ngram_size": 3,
    "length_penalty": 0.8,  # Prefer shorter responses
    "early_stopping": True,
}

# Memory Configuration (Enhanced)
MEMORY_CONFIG = {
    "max_short_term": 5,  # Recent exchanges to keep
    "max_important_info": 20,  # Explicitly remembered items
    "max_user_facts": 50,  # Facts about the user
    "context_token_budget": 500,  # Reduced for efficiency
    "auto_summarize": True,
    "persistence_file": DATA_DIR / "mobius_memory.json"
}

# Web Search Configuration
WEB_SEARCH_CONFIG = {
    "default_engine": "duckduckgo",  # Privacy-focused
    "max_results": 3,
    "timeout": 10,
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "rate_limit": 1.0  # Seconds between requests
}

# TTS Configuration
TTS_CONFIG = {
    "enabled": False,  # Default off
    "engine": "pyttsx3",  # or "gTTS" for Google TTS
    "voice_rate": 180,  # Words per minute
    "volume": 0.9,
    "voice_gender": "female",  # or "male"
    "streaming_mode": True,  # Stream speech as text generates
    "sentence_buffer": True,  # Speak complete sentences
}

# Performance Settings
PERFORMANCE_CONFIG = {
    "use_cache": True,
    "clear_cache_after_generation": False,
    "max_context_length": 2048,  # Reduced for speed
    "batch_size": 1,
    "num_threads": 4,
    "response_timeout": 30,
}

# System Settings
SYSTEM_CONFIG = {
    "allow_file_operations": True,
    "allow_web_access": True,
    "safe_mode": True,  # Restrict dangerous operations
    "auto_save_memory": True,
    "save_interval": 5,  # Save memory every 5 exchanges
    "safe_directories": [
        ".",  # Current directory
        "./data",
        "./logs",
        "./docs",
        "./mobius"
    ],
    "blocked_commands": [
        "rm", "del", "format", "fdisk", "mkfs",
        "shutdown", "reboot", "halt", "poweroff",
        "sudo", "su", "chmod 777", "chown",
        "wget", "curl", "nc", "netcat",
        "dd", "shred", "wipe"
    ]
}

# API Keys (from environment)
API_KEYS = {
    "huggingface": os.getenv("HF_TOKEN"),
    "openai": os.getenv("OPENAI_API_KEY"),  # For fallback
    "serper": os.getenv("SERPER_API_KEY"),  # For better search
    "elevenlabs": os.getenv("ELEVENLABS_API_KEY"),  # For better TTS
}

# Quick Settings Presets
PRESETS = {
    "fast": {
        "description": "Fastest responses, may be less detailed",
        "max_new_tokens": 256,
        "temperature": 0.5,
        "personality": "concise"
    },
    "balanced": {
        "description": "Balance between speed and quality",
        "max_new_tokens": 512,
        "temperature": 0.6,
        "personality": "friendly"
    },
    "quality": {
        "description": "Best quality, may be slower",
        "max_new_tokens": 1024,
        "temperature": 0.7,
        "personality": "technical"
    }
}

def apply_preset(preset_name: str):
    """Apply a configuration preset"""
    if preset_name in PRESETS:
        preset = PRESETS[preset_name]
        GENERATION_CONFIG["max_new_tokens"] = preset["max_new_tokens"]
        GENERATION_CONFIG["temperature"] = preset["temperature"]
        PERSONALITY_CONFIG["current_personality"] = preset["personality"]
        return True
    return False

def get_current_settings():
    """Get current configuration summary"""
    return {
        "personality": PERSONALITY_CONFIG["current_personality"],
        "max_tokens": GENERATION_CONFIG["max_new_tokens"],
        "temperature": GENERATION_CONFIG["temperature"],
        "model": MODEL_CONFIG["model_name"],
        "device": MODEL_CONFIG["device"]
    }

def validate_config():
    """Validate configuration settings"""
    errors = []

    # Check required API keys
    if not API_KEYS["huggingface"]:
        errors.append("HF_TOKEN environment variable not set")

    # Check model config
    if MODEL_CONFIG["device"] == "cuda" and not torch.cuda.is_available():
        errors.append("CUDA not available but device set to 'cuda'")

    # Check paths
    if not DATA_DIR.exists():
        try:
            DATA_DIR.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            errors.append(f"Cannot create data directory: {e}")

    if not LOGS_DIR.exists():
        try:
            LOGS_DIR.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            errors.append(f"Cannot create logs directory: {e}")

    return errors

# Import torch for validation
try:
    import torch
except ImportError:
    torch = None